# Dashboard模块独立数据源配置
# 该配置与环境无关，dev和prod环境都使用相同配置

spring:
  datasource:
    druid:
      dashboard:
        enabled: true
        url: ****************************************************************************************************************
        username: root
        password: Hss@redpad123
        driver-class-name: com.mysql.cj.jdbc.Driver
        
        # 连接池配置
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        
        # 监控配置
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        
        # 过滤器配置
        filter:
          stat:
            enabled: true
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: false
          wall:
            enabled: true
            config:
              multi-statement-allow: true

# Dashboard模块业务配置
dashboard:
  # 缓存配置
  cache:
    enabled: true
    expire: 3600
    prefix: "dashboard:"
  
  # 分页配置
  page:
    size: 20
    maxSize: 100
  
  # 数据权限配置
  permission:
    enabled: true
    defaultRole: "user"
  
  # 时间范围配置
  time:
    defaultRange: 30
    maxRange: 365
  
  # 异步处理配置
  async:
    enabled: true
    threadPoolSize: 10
  
  # 数据导出配置
  export:
    enabled: true
    maxRows: 100000
  
  # 统计数据配置
  statistics:
    refreshInterval: 30
    batchSize: 1000
  
  # Redis缓存配置
  redis:
    cachePrefix: "dashboard:"
    expire: 3600

# MyBatis配置
mybatis-plus:
  mapper-locations: classpath*:mapper/dashboard/*.xml
  type-aliases-package: com.redbook.dashboard.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: delFlag
      logic-delete-value: 2
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.redbook.dashboard: debug
    com.redbook.dashboard.mapper: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'